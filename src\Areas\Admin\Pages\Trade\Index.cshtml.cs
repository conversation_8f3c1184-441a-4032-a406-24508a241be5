using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;

namespace RazeWinComTr.Areas.Admin.Pages.Trade;

public class IndexModel : PageModel
{
    private readonly TradeService _tradeService;

    public IndexModel(TradeService tradeService)
    {
        _tradeService = tradeService;
    }

    public List<TradeViewModel> Trades { get; set; } = new();

    public async Task OnGetAsync()
    {
        Trades = await _tradeService.GetListAsync();
    }
}
