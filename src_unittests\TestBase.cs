using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Tests.Mocks;

namespace RazeWinComTr.Tests
{
    /// <summary>
    /// Base class for all test classes in the RazeWinComTr.Tests namespace.
    /// Provides common functionality for creating test database contexts, mock services, and loggers.
    /// </summary>
    public abstract class TestBase
    {
        /// <summary>
        /// Creates a new in-memory database context for testing.
        /// Each test should use a unique database name to ensure test isolation.
        /// </summary>
        /// <param name="databaseName">A unique name for the in-memory database</param>
        /// <returns>A new AppDbContext instance connected to an in-memory database</returns>
        protected AppDbContext CreateDbContext(string databaseName)
        {
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName)
                .ConfigureWarnings(w => w.Ignore(InMemoryEventId.TransactionIgnoredWarning))
                .Options;

            var context = new AppDbContext(options);
            context.Database.EnsureDeleted();
            context.Database.EnsureCreated();
            return context;
        }

        /// <summary>
        /// Creates a mock logger for testing.
        /// </summary>
        /// <typeparam name="T">The type that the logger is for</typeparam>
        /// <returns>A mock ILogger instance</returns>
        protected Mock<ILogger<T>> CreateMockLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// Creates a mock WalletService for testing.
        /// </summary>
        /// <returns>A MockWalletService instance</returns>
        protected MockWalletService CreateMockWalletService()
        {
            return new MockWalletService();
        }

        /// <summary>
        /// Creates a mock TokenPriceService for testing using Moq.
        /// The mock is configured to return the specified RZW price for all RZW price-related calls.
        /// </summary>
        /// <param name="buyPrice">The RZW price to return (default: 1.0)</param>
        /// <param name="coinId">The coin to return (default: 1)</param>
        /// <param name="sellPrice">The sell price to return (if null, defaults to buyPrice * 0.95)</param>
        /// <returns>A Mock&lt;ITokenPriceService&gt; instance</returns>
        protected Mock<ITokenPriceService> CreateMockTokenPriceService(decimal buyPrice = 1.0m, int coinId = 1, decimal? sellPrice = null)
        {
            var sellPriceValue = sellPrice ?? buyPrice * 0.95m;
            var mock = new Mock<ITokenPriceService>();

            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = coinId,
                BuyPrice = buyPrice,
                SellPrice = sellPriceValue
            };

            mock.Setup(x => x.GetCurrentRzwBuyPriceAsync())
                .ReturnsAsync(buyPrice);

            mock.Setup(x => x.GetRzwTokenIdAsync())
                .ReturnsAsync(coinId);

            mock.Setup(x => x.GetRzwTokenInfoAsync())
                .ReturnsAsync(rzwTokenInfo);

            mock.Setup(x => x.GetCoinInfoAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => new RzwTokenInfo
                {
                    TokenId = id,
                    BuyPrice = buyPrice,
                    SellPrice = sellPriceValue
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock TradeService for testing using Moq.
        /// The mock is configured with basic setup for CreateAsync method.
        /// </summary>
        /// <returns>A Mock&lt;ITradeService&gt; instance</returns>
        protected Mock<ITradeService> CreateMockTradeService()
        {
            var mock = new Mock<ITradeService>();
            var tradeId = 1;

            // Setup CreateAsync to return the trade with an assigned ID and CreatedDate
            mock.Setup(x => x.CreateAsync(It.IsAny<Trade>()))
                .ReturnsAsync((Trade trade) =>
                {
                    trade.Id = tradeId++;
                    trade.CreatedDate = DateTime.UtcNow;
                    return trade;
                });

            return mock;
        }

        /// <summary>
        /// Creates a legacy mock TradeService for testing.
        /// This method is obsolete. Use CreateMockTradeService() instead.
        /// </summary>
        /// <returns>A MockTradeService instance</returns>
        [Obsolete("Use CreateMockTradeService() method instead. This provides better flexibility with Moq framework.")]
        protected MockTradeService CreateLegacyMockTradeService()
        {
            return new MockTradeService();
        }

        /// <summary>
        /// Creates a legacy mock TradeService for testing with AppDbContext.
        /// This method is obsolete. Use CreateMockTradeService() instead.
        /// </summary>
        /// <param name="appDbContext">The database context</param>
        /// <returns>A MockTradeService instance</returns>
        [Obsolete("Use CreateMockTradeService() method instead. This provides better flexibility with Moq framework.")]
        protected MockTradeService CreateLegacyMockTradeService(AppDbContext appDbContext)
        {
            return new MockTradeService(appDbContext);
        }
    }
}
