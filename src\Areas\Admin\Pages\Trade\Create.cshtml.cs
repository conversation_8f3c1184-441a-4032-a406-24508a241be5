using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Market;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.Trade;

public class CreateModel : PageModel
{
    private readonly TradeService _tradeService;
    private readonly IWalletService _walletService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        TradeService tradeService,
        IWalletService walletService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _tradeService = tradeService;
        _walletService = walletService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public TradeCreateViewModel ViewEntity { get; set; } = new();

    public List<UserViewModel> Users { get; set; } = new();
    public List<MarketViewModel> Coins { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task OnGetAsync()
    {
        Users = await _context.Users
            .Where(u => u.IsActive == 1)
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .ToListAsync();

        Coins = await _context.Markets
            .Where(m => m.IsActive == 1)
            .Select(m => new MarketViewModel
            {
                Id = m.Id,
                Name = m.Name,
                PairCode = m.PairCode
            })
            .ToListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            // Get user's current balances
            var tryWallet = await _walletService.GetByUserIdAndCoinIdAsync(ViewEntity.UserId, 1); // Assuming TRY has ID 1
            var coinWallet = await _walletService.GetByUserIdAndCoinIdAsync(ViewEntity.UserId, ViewEntity.CoinId);

            // Find the trading pair wallet if it exists
            var pairWallet = await _walletService.GetByUserIdAndCoinIdAsync(ViewEntity.UserId, ViewEntity.CoinId);
            if (pairWallet == null)
            {
                //create new wallet with zero balance
                pairWallet = new DbModel.Wallet
                {
                    UserId = ViewEntity.UserId,
                    CoinId = ViewEntity.CoinId,
                    Balance = 0,
                    CreatedDate = DateTime.UtcNow
                };
                await _walletService.CreateAsync(pairWallet);
            }


            decimal tryBalance = tryWallet?.Balance ?? 0;
            decimal coinBalance = coinWallet?.Balance ?? 0;
            decimal pairWalletBalance = pairWallet?.Balance ?? 0;

            // Calculate new balances based on trade type
            decimal newTryBalance;
            decimal newCoinBalance;
            decimal newPairWalletBalance = pairWalletBalance; // Default to no change
            decimal totalAmount = ViewEntity.Amount * ViewEntity.Rate;

            if (ViewEntity.Type == 0) // Buy
            {
                // Check if user has enough TRY
                if (tryBalance < totalAmount)
                {
                    ModelState.AddModelError("", _localizer["User does not have enough TRY balance"]);
                    await OnGetAsync();
                    return Page();
                }

                newTryBalance = tryBalance - totalAmount;
                newCoinBalance = coinBalance + ViewEntity.Amount;
            }
            else // Sell
            {
                // Check if user has enough coin
                if (coinBalance < ViewEntity.Amount)
                {
                    ModelState.AddModelError("", _localizer["User does not have enough coin balance"]);
                    await OnGetAsync();
                    return Page();
                }

                newTryBalance = tryBalance + totalAmount;
                newCoinBalance = coinBalance - ViewEntity.Amount;
            }

            var entity = new DbModel.Trade
            {
                UserId = ViewEntity.UserId,
                CoinId = ViewEntity.CoinId,
                Type = (TradeType)ViewEntity.Type,
                CoinAmount = ViewEntity.Amount,
                CoinRate = ViewEntity.Rate,
                TryAmount = ViewEntity.Amount * ViewEntity.Rate,
                PreviousBalance = tryBalance,
                NewBalance = newTryBalance,
                PreviousCoinBalance = coinBalance,
                NewCoinBalance = newCoinBalance,
                PreviousWalletBalance = pairWalletBalance,
                NewWalletBalance = newPairWalletBalance,
                CreatedDate = DateTime.UtcNow
            };

            await _tradeService.CreateAsync(entity);

            // Update wallets
            if (tryWallet == null)
            {
                tryWallet = new DbModel.Wallet
                {
                    UserId = ViewEntity.UserId,
                    CoinId = 1, // TRY
                    Balance = newTryBalance,
                    CreatedDate = DateTime.UtcNow
                };
                await _walletService.CreateAsync(tryWallet);
            }
            else
            {
                tryWallet.Balance = newTryBalance;
                tryWallet.ModifiedDate = DateTime.UtcNow;
                await _walletService.UpdateAsync(tryWallet);
            }

            if (coinWallet == null)
            {
                coinWallet = new DbModel.Wallet
                {
                    UserId = ViewEntity.UserId,
                    CoinId = ViewEntity.CoinId,
                    Balance = newCoinBalance,
                    CreatedDate = DateTime.UtcNow
                };
                await _walletService.CreateAsync(coinWallet);
            }
            else
            {
                coinWallet.Balance = newCoinBalance;
                coinWallet.ModifiedDate = DateTime.UtcNow;
                await _walletService.UpdateAsync(coinWallet);
            }

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Trade successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Trade"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            await OnGetAsync();
            return Page();
        }
    }
}

public class TradeCreateViewModel
{
    public int UserId { get; set; }
    public int CoinId { get; set; }
    public int Type { get; set; } = 0; // 0: Buy, 1: Sell
    public decimal Amount { get; set; } = 0;
    public decimal Rate { get; set; } = 0;
}
