using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Tests.Basic
{
    /// <summary>
    /// Tests for the ReferralRewardService's summary functionality.
    /// These tests verify the correct retrieval and calculation of reward summaries
    /// for both individual users and specific deposits.
    /// </summary>
    public class ReferralRewardSummaryTests : TestBase
    {
        /// <summary>
        /// Tests that the reward summary is correctly retrieved for a deposit with existing rewards.
        /// This test creates a deposit with multiple rewards at different levels and verifies that
        /// the summary contains the correct information about each reward.
        ///
        /// The test verifies:
        /// - The total reward amount is correct
        /// - The number of rewarded users is correct
        /// - Each reward's details (user, level, amount, percentage) are correct
        /// - The deposit status is correctly reflected
        /// </summary>
        [Fact]
        public async Task GetPaymentRewardSummaryAsync_WithExistingRewards_ReturnsSummaryCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("GetPaymentRewardSummaryAsync_WithExistingRewards_ReturnsSummaryCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create users
            var referrer1 = TestDataGenerator.CreateUser(1);
            var referrer2 = TestDataGenerator.CreateUser(2, "REF2", referrer1.UserId);
            var user = TestDataGenerator.CreateUser(3, "REF3", referrer2.UserId);

            dbContext.Users.Add(referrer1);
            dbContext.Users.Add(referrer2);
            dbContext.Users.Add(user);

            // Create packages
            var package1 = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            var package2 = TestDataGenerator.CreatePackage(2, "Gold", 500m);
            dbContext.Packages.Add(package1);
            dbContext.Packages.Add(package2);

            // Create a deposit
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m, rewardStatus: DepositRewardStatus.Distributed);
            dbContext.Deposits.Add(deposit);

            // Create rewards
            var reward1 = TestDataGenerator.CreateReferralReward(1, referrer1.UserId, user.UserId, 1, 1, 200m, 20m, 1000m);
            var reward2 = TestDataGenerator.CreateReferralReward(2, referrer2.UserId, user.UserId, 1, 2, 150m, 15m, 1000m);

            // Set deposit ID
            reward1.DepositId = deposit.Id;
            reward2.DepositId = deposit.Id;

            dbContext.ReferralRewards.Add(reward1);
            dbContext.ReferralRewards.Add(reward2);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var summary = await service.GetDepositRewardSummaryAsync(deposit.Id);

            // Assert
            Assert.NotNull(summary);
            Assert.Equal(deposit.Id, summary.DepositId);
            Assert.Equal(1000m, summary.DepositAmount);
            Assert.Equal(DepositRewardStatus.Distributed, summary.RewardStatus);
            Assert.Equal(2, summary.RewardedUsersCount);
            Assert.Equal(350m, summary.TotalRzwDistributed); // 200 + 150 = 350

            // Verify reward details
            Assert.Equal(2, summary.Rewards.Count);

            var firstReward = summary.Rewards.First();
            Assert.Equal(referrer1.UserId, firstReward.UserId);
            Assert.Equal(1, firstReward.Level);
            Assert.Equal(20m, firstReward.RzwPercentage);
            Assert.Equal(200m, firstReward.RzwAmount);

            var secondReward = summary.Rewards.Last();
            Assert.Equal(referrer2.UserId, secondReward.UserId);
            Assert.Equal(2, secondReward.Level);
            Assert.Equal(15m, secondReward.RzwPercentage);
            Assert.Equal(150m, secondReward.RzwAmount);
        }

        /// <summary>
        /// Tests that the reward summary is correctly retrieved for a deposit with no rewards.
        /// This test creates a deposit but does not create any rewards for it, and verifies that
        /// the summary correctly reflects the absence of rewards.
        ///
        /// The test verifies:
        /// - The total reward amount is 0
        /// - The number of rewarded users is 0
        /// - The rewards list is empty
        /// - The deposit status is correctly reflected
        /// </summary>
        [Fact]
        public async Task GetPaymentRewardSummaryAsync_WithNoRewards_ReturnsEmptySummary()
        {
            // Arrange
            var dbContext = CreateDbContext("GetPaymentRewardSummaryAsync_WithNoRewards_ReturnsEmptySummary");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create a user
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);

            // Create a deposit with NoRewards status
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m, rewardStatus: DepositRewardStatus.NoRewards);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var summary = await service.GetDepositRewardSummaryAsync(deposit.Id);

            // Assert
            Assert.NotNull(summary);
            Assert.Equal(deposit.Id, summary.DepositId);
            Assert.Equal(1000m, summary.DepositAmount);
            Assert.Equal(DepositRewardStatus.NoRewards, summary.RewardStatus);
            Assert.Equal(0, summary.RewardedUsersCount);
            Assert.Equal(0m, summary.TotalRzwDistributed);
            Assert.Empty(summary.Rewards);
        }

        /// <summary>
        /// Tests that an exception is thrown when trying to get a reward summary for a non-existent deposit.
        /// This test attempts to retrieve a summary for a deposit with an ID that doesn't exist in the database.
        ///
        /// The test verifies that an ArgumentException is thrown, as the deposit must exist
        /// in order to retrieve its reward summary.
        /// </summary>
        [Fact]
        public async Task GetPaymentRewardSummaryAsync_WithNonExistentPayment_ThrowsException()
        {
            // Arrange
            var dbContext = CreateDbContext("GetPaymentRewardSummaryAsync_WithNonExistentPayment_ThrowsException");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService(dbContext);

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService);

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(
                async () => await service.GetDepositRewardSummaryAsync(999));
        }

        [Fact]
        public async Task GetUserRewardSummaryAsync_WithExistingRewards_ReturnsSummaryCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("GetUserReferralRewardSummaryAsync_WithExistingRewards_ReturnsSummaryCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService(dbContext);

            // Create users
            var user1 = TestDataGenerator.CreateUser(1);
            var user2 = TestDataGenerator.CreateUser(2);
            var user3 = TestDataGenerator.CreateUser(3);
            var user4 = TestDataGenerator.CreateUser(4);

            dbContext.Users.AddRange(user1, user2, user3, user4);

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create payments
            var payment1 = TestDataGenerator.CreateDeposit(1, 2, 1000m);
            var payment2 = TestDataGenerator.CreateDeposit(2, 3, 2000m);
            var payment3 = TestDataGenerator.CreateDeposit(3, 4, 3000m);

            dbContext.Deposits.AddRange(payment1, payment2, payment3);

            // Create rewards for user1 from different levels
            var reward1 = TestDataGenerator.CreateReferralReward(1, 1, 2, 1, 1, 200m, 20m, 1000m);
            var reward2 = TestDataGenerator.CreateReferralReward(2, 1, 3, 1, 2, 300m, 15m, 2000m);
            var reward3 = TestDataGenerator.CreateReferralReward(3, 1, 4, 1, 3, 300m, 10m, 3000m);

            reward1.DepositId = payment1.Id;
            reward2.DepositId = payment2.Id;
            reward3.DepositId = payment3.Id;

            dbContext.ReferralRewards.AddRange(reward1, reward2, reward3);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService);

            // Act
            var result = await service.GetUserRewardSummaryAsync(user1.UserId);

            // Assert
            Assert.Equal(800m, result.TotalRzwRewards); // 200 + 300 + 300 = 800
            Assert.Equal(3, result.TotalReferrals);

            // Check rewards by level
            Assert.Equal(3, result.RzwRewardsByLevel.Count);
            Assert.Equal(200m, result.RzwRewardsByLevel[1]); // Level 1: 200
            Assert.Equal(300m, result.RzwRewardsByLevel[2]); // Level 2: 300
            Assert.Equal(300m, result.RzwRewardsByLevel[3]); // Level 3: 300

            // Check referrals by level
            Assert.Equal(3, result.ReferralsByLevel.Count);
            Assert.Equal(1, result.ReferralsByLevel[1]); // 1 referral at level 1
            Assert.Equal(1, result.ReferralsByLevel[2]); // 1 referral at level 2
            Assert.Equal(1, result.ReferralsByLevel[3]); // 1 referral at level 3
        }

        [Fact]
        public async Task GetUserRewardSummaryAsync_WithNoRewards_ReturnsSummaryWithZeroCounts()
        {
            // Arrange
            var dbContext = CreateDbContext("GetUserReferralRewardSummaryAsync_WithNoRewards_ReturnsSummaryWithZeroCounts");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService(dbContext);

            // Create a user with no rewards
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService);

            // Act
            var result = await service.GetUserRewardSummaryAsync(user.UserId);

            // Assert
            Assert.Equal(0m, result.TotalRzwRewards);
            Assert.Equal(0, result.TotalReferrals);
            Assert.Empty(result.RzwRewardsByLevel);
            Assert.Empty(result.ReferralsByLevel);
        }

        [Fact]
        public async Task GetUserRewardSummaryAsync_WithNonExistentUser_ThrowsException()
        {
            // Arrange
            var dbContext = CreateDbContext("GetUserReferralRewardSummaryAsync_WithNonExistentUser_ThrowsException");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService(dbContext);

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService);

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(
                async () => await service.GetUserRewardSummaryAsync(999));
        }
    }
}
