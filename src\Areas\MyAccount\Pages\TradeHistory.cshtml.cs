using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class TradeHistoryModel : PageModel
{
    private readonly TradeService _tradeService;
    private readonly AppDbContext _context;

    public TradeHistoryModel(TradeService tradeService, AppDbContext context)
    {
        _tradeService = tradeService;
        _context = context;
        Trades = new List<TradeViewModel>();
    }

    public List<TradeViewModel> Trades { get; set; }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user information
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user != null)
        {
            UserFullName = $"{user.Name} {user.Surname}";
            UserEmail = user.Email;
            UserPhone = user.PhoneNumber;
            UserCreatedDate = user.CrDate;
        }

        Trades = await _tradeService.GetByUserIdAsync(userId.Value);
        return Page();
    }
}
