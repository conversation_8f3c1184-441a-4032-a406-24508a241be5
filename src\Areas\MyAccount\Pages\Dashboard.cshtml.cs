using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class DashboardModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly TradeService _tradeService;
    private readonly IWalletService _walletService;

    public DashboardModel(AppDbContext context, TradeService tradeService, IWalletService walletService)
    {
        _context = context;
        _tradeService = tradeService;
        _walletService = walletService;
        RecentTrades = new List<TradeViewModel>();
        WalletBalances = new List<WalletViewModel>();
    }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }
    public List<TradeViewModel> RecentTrades { get; set; }
    public List<WalletViewModel> WalletBalances { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user information
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        UserFullName = $"{user.Name} {user.Surname}";
        UserEmail = user.Email;
        UserPhone = user.PhoneNumber;
        UserCreatedDate = user.CrDate;

        // Get recent trades (last 5)
        RecentTrades = await _tradeService.GetByUserIdAsync(userId.Value);
        RecentTrades = RecentTrades.Take(5).ToList(); // Bu zaten memory'de, async gerekmiyor

        // Get wallet balances (top 5)
        var wallets = await _walletService.GetTopNByUserIdAsync(userId.Value, 5);

        WalletBalances = wallets
            .Select(w => new WalletViewModel
            {
                Id = w.Id,
                UserId = w.UserId,
                UserEmail = user.Email,
                CoinId = w.CoinId,
                CoinName = w.Coin.Name,
                CoinCode = w.Coin.PairCode,
                Balance = w.Balance,
                CreatedDate = w.CreatedDate,
                ModifiedDate = w.ModifiedDate,
                CoinPrice = w.Coin?.BuyPrice ?? 0
            })
            .ToList(); // Bu zaten memory'de, async gerekmiyor

        return Page();
    }
}
