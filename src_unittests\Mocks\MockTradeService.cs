using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of TradeService for testing
/// This class is obsolete. Use CreateMockTradeService() method in TestBase instead.
/// </summary>
[Obsolete("Use CreateMockTradeService() method in TestBase instead. This provides better flexibility with Moq framework.")]
public class MockTradeService : TradeService
{
    private readonly List<Trade> _trades = new();
    private int _nextId = 1;

    public MockTradeService() : base(
        new Mock<IStringLocalizer<SharedResource>>().Object,
        new Mock<AppDbContext>(new DbContextOptions<AppDbContext>()).Object)
    {
    }
    public MockTradeService(AppDbContext appDbContext) : base(new Mock<IStringLocalizer<SharedResource>>().Object, appDbContext)
    {
    }
    // Track method calls for verification
    public int CreateAsyncCallCount { get; private set; }
    public List<Trade> CreateAsyncCalls { get; } = new();

    public override Task<Trade> CreateAsync(Trade trade)
    {
        trade.Id = _nextId++;
        trade.CreatedDate = DateTime.UtcNow;
        _trades.Add(trade);
        CreateAsyncCallCount++;
        CreateAsyncCalls.Add(trade);
        return Task.FromResult(trade);
    }

    public bool VerifyCreateAsync(Trade expectedTrade)
    {
        return CreateAsyncCalls.Any(t =>
            t.UserId == expectedTrade.UserId &&
            t.CoinId == expectedTrade.CoinId &&
            t.CoinAmount == expectedTrade.CoinAmount &&
            t.TryAmount == expectedTrade.TryAmount &&
            t.Type == expectedTrade.Type);
    }
}