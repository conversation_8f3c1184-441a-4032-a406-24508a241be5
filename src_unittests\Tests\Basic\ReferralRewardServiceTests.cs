using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace RazeWinComTr.Tests.Basic
{
    /// <summary>
    /// Tests for the ReferralRewardService class.
    /// These tests verify the basic functionality of the reward distribution system,
    /// including handling of various edge cases and error conditions.
    /// </summary>
    public class ReferralRewardServiceTests : TestBase
    {
        /// <summary>
        /// Tests that rewards are distributed correctly in a valid referral chain.
        /// This test creates a 4-level referral chain with reward percentages defined for the first 3 levels.
        ///
        /// When the bottom user makes a deposit of 1000 TL, the rewards should be distributed as follows:
        /// - Level 1 (direct referrer): 20% = 200 RZW
        /// - Level 2 (second level): 15% = 150 RZW
        /// - Level 3 (third level): 10% = 100 RZW
        ///
        /// The test verifies that the correct reward amounts are distributed to the correct users
        /// and that the deposit status is updated to Distributed.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithValidReferralChain_DistributesRzwRewardsCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithValidReferralChain_DistributesRewardsCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m)); // 20% RZW, 0% TL for level 1
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(2, 1, 2, 15m, 0m)); // 15% RZW, 0% TL for level 2
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(3, 1, 3, 10m, 0m)); // 10% RZW, 0% TL for level 3

            // Create a 3-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 4, 1, 1);

            // Create a deposit for the bottom user
            var deposit = TestDataGenerator.CreateDeposit(1, userIds.Last(), 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(3, result.RewardedUsersCount);
            Assert.Equal(450m, result.TotalRzwDistributed); // 200 + 150 + 100 = 450 (actual RZW amounts)
            Assert.Equal(0m, result.TotalTlDistributed); // 0 + 0 + 0 = 0 (actual TL amounts)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Equal(3, rewards.Count);

            // Level 1 reward (20%)
            var level1Reward = rewards.FirstOrDefault(r => r.Level == 1);
            Assert.NotNull(level1Reward);
            Assert.Equal(userIds[2], level1Reward.UserId);
            Assert.Equal(userIds[3], level1Reward.ReferredUserId);
            Assert.Equal(20m, level1Reward.RzwPercentage); // 100% of 20% = 20%
            Assert.Equal(0m, level1Reward.TlPercentage); // 0% of 20% = 0%
            Assert.Equal(200m, level1Reward.RzwAmount); // 1000 * 0.20 = 200
            Assert.Equal(0m, level1Reward.TlAmount); // 1000 * 0 = 0

            // Level 2 reward (15%)
            var level2Reward = rewards.FirstOrDefault(r => r.Level == 2);
            Assert.NotNull(level2Reward);
            Assert.Equal(userIds[1], level2Reward.UserId);
            Assert.Equal(userIds[3], level2Reward.ReferredUserId);
            Assert.Equal(15m, level2Reward.RzwPercentage); // 100% of 15% = 15%
            Assert.Equal(0m, level2Reward.TlPercentage); // 0% of 15% = 0%
            Assert.Equal(150m, level2Reward.RzwAmount); // 1000 * 0.15 = 150
            Assert.Equal(0m, level2Reward.TlAmount); // 1000 * 0 = 0

            // Level 3 reward (10%)
            var level3Reward = rewards.FirstOrDefault(r => r.Level == 3);
            Assert.NotNull(level3Reward);
            Assert.Equal(userIds[0], level3Reward.UserId);
            Assert.Equal(userIds[3], level3Reward.ReferredUserId);
            Assert.Equal(10m, level3Reward.RzwPercentage); // 100% of 10% = 10%
            Assert.Equal(0m, level3Reward.TlPercentage); // 0% of 10% = 0%
            Assert.Equal(100m, level3Reward.RzwAmount); // 1000 * 0.10 = 100
            Assert.Equal(0m, level3Reward.TlAmount); // 1000 * 0 = 0

            // Verify wallet service was called
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[2], rzwTokenId, 200m));
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[1], rzwTokenId, 150m));
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[0], rzwTokenId, 100m));

            // Verify trade records were created
            Assert.Equal(3, mockTradeService.CreateAsyncCallCount);
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[2],
                CoinId = rzwTokenId,
                CoinAmount = 200m, // Actual RZW amount
                TryAmount = 200m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[1],
                CoinId = rzwTokenId,
                CoinAmount = 150m, // Actual RZW amount
                TryAmount = 150m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[0],
                CoinId = rzwTokenId,
                CoinAmount = 100m, // Actual RZW amount
                TryAmount = 100m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that no rewards are distributed when a user has no referrers.
        /// This test creates a single user with no referrer who makes a deposit.
        ///
        /// The test verifies that:
        /// - No rewards are created
        /// - The wallet service is not called
        /// - The deposit status is updated to NoRewards
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithNoReferrers_ReturnsNoRewards()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithNoReferrers_ReturnsNoRewards");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create a user with no referrer
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);

            // Create a deposit for the user
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(0, result.RewardedUsersCount);
            Assert.Equal(0m, result.TotalRzwDistributed);

            // Verify no rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Empty(rewards);

            // Verify wallet service was not called
            Assert.Equal(0, mockWalletService.AddToWalletAsyncCallCount);

            // Verify trade service was not called
            Assert.Equal(0, mockTradeService.CreateAsyncCallCount);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.NoRewards, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that no rewards are distributed when referrers don't have packages.
        /// This test creates a referral chain with two users, but neither has a package.
        ///
        /// The test verifies that:
        /// - No rewards are created
        /// - The wallet service is not called
        /// - The deposit status is updated to NoRewards
        ///
        /// This test confirms that having a package is a requirement for receiving rewards.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithReferrersButNoPackages_ReturnsNoRewards()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithReferrersButNoPackages_ReturnsNoRewards");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create a referral chain but without packages
            var referrer = TestDataGenerator.CreateUser(1);
            var user = TestDataGenerator.CreateUser(2, "REF2", referrer.UserId);

            dbContext.Users.Add(referrer);
            dbContext.Users.Add(user);

            // Create a deposit for the user
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(0, result.RewardedUsersCount);
            Assert.Equal(0m, result.TotalRzwDistributed);

            // Verify no rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Empty(rewards);

            // Verify wallet service was not called
            Assert.Equal(0, mockWalletService.AddToWalletAsyncCallCount);

            // Verify trade service was not called
            Assert.Equal(0, mockTradeService.CreateAsyncCallCount);

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.NoRewards, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that levels with no reward percentage are skipped during reward distribution.
        /// This test creates a 4-level referral chain with reward percentages defined only for levels 1 and 3.
        ///
        /// When the bottom user makes a deposit of 1000 TL, the rewards should be distributed as follows:
        /// - Level 1 (direct referrer): 20% = 200 RZW
        /// - Level 2 (second level): No reward (no percentage defined)
        /// - Level 3 (third level): 10% = 100 RZW
        ///
        /// The test verifies that only levels with defined percentages receive rewards,
        /// and levels with no percentage are skipped.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithMissingRewardPercentages_SkipsLevelsWithNoPercentage()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithMissingRewardPercentages_SkipsLevelsWithNoPercentage");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages - only for levels 1 and 3
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 20m, 0m)); // 20% RZW, 0% TL for level 1
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(2, 1, 3, 10m, 0m)); // 10% RZW, 0% TL for level 3
            // No percentage for level 2

            // Create a 3-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 4, 1, 1);

            // Create a deposit for the bottom user
            var deposit = TestDataGenerator.CreateDeposit(1, userIds.Last(), 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(2, result.RewardedUsersCount); // Only 2 users should get rewards
            Assert.Equal(300m, result.TotalRzwDistributed); // 200 + 100 = 300 (actual RZW amounts)
            Assert.Equal(0m, result.TotalTlDistributed); // 0 + 0 = 0 (actual TL amounts)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Equal(2, rewards.Count);

            // Level 1 reward (20%)
            var level1Reward = rewards.FirstOrDefault(r => r.Level == 1);
            Assert.NotNull(level1Reward);
            Assert.Equal(userIds[2], level1Reward.UserId);
            Assert.Equal(userIds[3], level1Reward.ReferredUserId);
            Assert.Equal(20m, level1Reward.RzwPercentage); // 100% of 20% = 20%
            Assert.Equal(0m, level1Reward.TlPercentage); // 0% of 20% = 0%
            Assert.Equal(200m, level1Reward.RzwAmount); // 1000 * 0.20 = 200
            Assert.Equal(0m, level1Reward.TlAmount); // 1000 * 0 = 0

            // No level 2 reward should exist
            var level2Reward = rewards.FirstOrDefault(r => r.Level == 2);
            Assert.Null(level2Reward);

            // Level 3 reward (10%)
            var level3Reward = rewards.FirstOrDefault(r => r.Level == 3);
            Assert.NotNull(level3Reward);
            Assert.Equal(userIds[0], level3Reward.UserId);
            Assert.Equal(userIds[3], level3Reward.ReferredUserId);
            Assert.Equal(10m, level3Reward.RzwPercentage); // 100% of 10% = 10%
            Assert.Equal(0m, level3Reward.TlPercentage); // 0% of 10% = 0%
            Assert.Equal(100m, level3Reward.RzwAmount); // 1000 * 0.10 = 100
            Assert.Equal(0m, level3Reward.TlAmount); // 1000 * 0 = 0

            // Verify wallet service was called for levels 1 and 3 only
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[2], rzwTokenId, 200m));
            Assert.DoesNotContain(mockWalletService.AddToWalletAsyncCalls, call => call.UserId == userIds[1]);
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[0], rzwTokenId, 100m));

            // Verify trade records were created for levels 1 and 3 only
            Assert.Equal(2, mockTradeService.CreateAsyncCallCount);
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[2],
                CoinId = rzwTokenId,
                CoinAmount = 200m, // Actual RZW amount
                TryAmount = 200m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[0],
                CoinId = rzwTokenId,
                CoinAmount = 100m, // Actual RZW amount
                TryAmount = 100m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that an exception is thrown when trying to process rewards for a non-approved deposit.
        /// This test creates a deposit with a Pending status and attempts to process rewards for it.
        ///
        /// The test verifies that an InvalidOperationException is thrown, as rewards should only
        /// be processed for approved payments.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithNonApprovedPayment_ThrowsException()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithNonApprovedPayment_ThrowsException");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create a user
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);

            // Create a deposit with non-approved status
            var deposit = TestDataGenerator.CreateDeposit(1, user.UserId, 1000m, DepositStatus.Pending);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(
                async () => await service.ProcessDepositRewardsAsync(deposit.Id));
        }

        /// <summary>
        /// Tests that rewards are distributed correctly in a valid referral chain with both RZW and TL rewards.
        /// This test creates a 4-level referral chain with reward percentages defined for the first 3 levels.
        ///
        /// When the bottom user makes a deposit of 1000 TL, the rewards should be distributed as follows:
        /// - Level 1 (direct referrer): 5% RZW + 15% TL = 50 RZW + 150 TL
        /// - Level 2 (second level): 3.75% RZW + 11.25% TL = 37.5 RZW + 112.5 TL
        /// - Level 3 (third level): 2.5% RZW + 7.5% TL = 25 RZW + 75 TL
        ///
        /// The test verifies that the correct reward amounts are distributed to the correct users
        /// and that the deposit status is updated to Distributed.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithValidReferralChain_DistributesMixedRewardsCorrectly()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithValidReferralChain_DistributesMixedRewardsCorrectly");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create packages
            var platinumPackage = TestDataGenerator.CreatePackage(1, "Platinum", 1000m);
            dbContext.Packages.Add(platinumPackage);

            // Create package reward percentages with mixed RZW and TL percentages
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(1, 1, 1, 5m, 15m)); // 5% RZW, 15% TL for level 1
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(2, 1, 2, 3.75m, 11.25m)); // 3.75% RZW, 11.25% TL for level 2
            dbContext.PackageRewardPercentages.Add(TestDataGenerator.CreatePackageRewardPercentage(3, 1, 3, 2.5m, 7.5m)); // 2.5% RZW, 7.5% TL for level 3

            // Create a 3-level referral chain
            var userIds = TestDataGenerator.CreateReferralChain(dbContext, 4, 1, 1);

            // Create a deposit for the bottom user
            var deposit = TestDataGenerator.CreateDeposit(1, userIds.Last(), 1000m);
            dbContext.Deposits.Add(deposit);
            dbContext.SaveChanges();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act
            var result = await service.ProcessDepositRewardsAsync(deposit.Id);

            // Assert
            Assert.Equal(3, result.RewardedUsersCount);
            Assert.Equal(112.5m, result.TotalRzwDistributed); // 50 + 37.5 + 25 = 112.5 (actual RZW amounts)
            Assert.Equal(337.5m, result.TotalTlDistributed); // 150 + 112.5 + 75 = 337.5 (actual TL amounts)

            // Verify rewards were created
            var rewards = await dbContext.ReferralRewards.ToListAsync();
            Assert.Equal(3, rewards.Count);

            // Level 1 reward (5% RZW, 15% TL)
            var level1Reward = rewards.FirstOrDefault(r => r.Level == 1);
            Assert.NotNull(level1Reward);
            Assert.Equal(userIds[2], level1Reward.UserId);
            Assert.Equal(userIds[3], level1Reward.ReferredUserId);
            Assert.Equal(5m, level1Reward.RzwPercentage);
            Assert.Equal(15m, level1Reward.TlPercentage);
            Assert.Equal(50m, level1Reward.RzwAmount); // 1000 * 0.05 = 50
            Assert.Equal(150m, level1Reward.TlAmount); // 1000 * 0.15 = 150

            // Level 2 reward (3.75% RZW, 11.25% TL)
            var level2Reward = rewards.FirstOrDefault(r => r.Level == 2);
            Assert.NotNull(level2Reward);
            Assert.Equal(userIds[1], level2Reward.UserId);
            Assert.Equal(userIds[3], level2Reward.ReferredUserId);
            Assert.Equal(3.75m, level2Reward.RzwPercentage);
            Assert.Equal(11.25m, level2Reward.TlPercentage);
            Assert.Equal(37.5m, level2Reward.RzwAmount); // 1000 * 0.0375 = 37.5
            Assert.Equal(112.5m, level2Reward.TlAmount); // 1000 * 0.1125 = 112.5

            // Level 3 reward (2.5% RZW, 7.5% TL)
            var level3Reward = rewards.FirstOrDefault(r => r.Level == 3);
            Assert.NotNull(level3Reward);
            Assert.Equal(userIds[0], level3Reward.UserId);
            Assert.Equal(userIds[3], level3Reward.ReferredUserId);
            Assert.Equal(2.5m, level3Reward.RzwPercentage);
            Assert.Equal(7.5m, level3Reward.TlPercentage);
            Assert.Equal(25m, level3Reward.RzwAmount); // 1000 * 0.025 = 25
            Assert.Equal(75m, level3Reward.TlAmount); // 1000 * 0.075 = 75

            // Verify wallet service was called
            var rzwTokenId = await mockTokenPriceService.Object.GetRzwTokenIdAsync();
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[2], rzwTokenId, 50m));
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[1], rzwTokenId, 37.5m));
            Assert.True(mockWalletService.VerifyAddToWalletAsync(userIds[0], rzwTokenId, 25m));

            // Verify trade records were created
            Assert.Equal(3, mockTradeService.CreateAsyncCallCount);
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[2],
                CoinId = rzwTokenId,
                CoinAmount = 50m, // Actual RZW amount
                TryAmount = 50m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[1],
                CoinId = rzwTokenId,
                CoinAmount = 37.5m, // Actual RZW amount
                TryAmount = 37.5m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));
            Assert.True(mockTradeService.VerifyCreateAsync(new Trade
            {
                UserId = userIds[0],
                CoinId = rzwTokenId,
                CoinAmount = 25m, // Actual RZW amount
                TryAmount = 25m, // RZW amount * RZW price (1.0)
                Type = TradeType.ReferralReward
            }));

            // Verify deposit status was updated
            var updatedPayment = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.NotNull(updatedPayment);
            Assert.Equal(DepositRewardStatus.Distributed, updatedPayment!.RewardStatus);
        }

        /// <summary>
        /// Tests that an exception is thrown when trying to process rewards for a non-existent deposit.
        /// This test attempts to process rewards for a deposit with an ID that doesn't exist in the database.
        ///
        /// The test verifies that an ArgumentException is thrown, as the deposit must exist
        /// in order to process rewards for it.
        /// </summary>
        [Fact]
        public async Task ProcessPaymentRewardsAsync_WithNonExistentPayment_ThrowsException()
        {
            // Arrange
            var dbContext = CreateDbContext("ProcessPaymentRewardsAsync_WithNonExistentPayment_ThrowsException");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(1.0m);
            var mockLogger = CreateMockLogger<ReferralRewardService>();
            var mockTradeService = CreateMockTradeService();

            // Create the service
            var service = new ReferralRewardService(
                dbContext,
                mockWalletService,
                mockTokenPriceService.Object,
                mockLogger.Object,
                mockTradeService.Object);

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(
                async () => await service.ProcessDepositRewardsAsync(999));
        }
    }
}
